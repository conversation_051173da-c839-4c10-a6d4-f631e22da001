#!/usr/bin/env python3
"""
PySocks一键安装脚本 - Python版本
解决SSH环境下SOCKS代理依赖问题
使用方法: python3 install_pysocks.py
"""

import os
import sys
import subprocess
import tempfile
import shutil
import urllib.request
import tarfile
from pathlib import Path

class Colors:
    """终端颜色"""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def log_info(msg):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {msg}")

def log_success(msg):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {msg}")

def log_warning(msg):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {msg}")

def log_error(msg):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {msg}")

def check_pysocks():
    """检查PySocks是否已安装"""
    log_info("检查PySocks是否已安装...")
    try:
        import socks
        log_success(f"PySocks已安装，版本: {socks.__version__}")
        return True
    except ImportError:
        log_info("PySocks未安装，开始安装...")
        return False

def download_file(url, filename):
    """下载文件"""
    log_info(f"下载文件: {filename}")
    try:
        urllib.request.urlretrieve(url, filename)
        file_size = os.path.getsize(filename)
        log_success(f"下载成功，文件大小: {file_size} 字节")
        return True
    except Exception as e:
        log_error(f"下载失败: {e}")
        return False

def extract_tarfile(filename, extract_to="."):
    """解压tar.gz文件"""
    log_info(f"解压文件: {filename}")
    try:
        with tarfile.open(filename, 'r:gz') as tar:
            tar.extractall(extract_to)
        log_success("解压成功")
        return True
    except Exception as e:
        log_error(f"解压失败: {e}")
        return False

def run_command(cmd, cwd=None):
    """运行命令"""
    log_info(f"执行命令: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            check=True
        )
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        log_error(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def install_pysocks():
    """安装PySocks"""
    log_info("开始安装PySocks...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        log_info(f"使用临时目录: {temp_dir}")
        
        # 下载源码
        url = "https://github.com/Anorov/PySocks/archive/refs/heads/master.tar.gz"
        tarfile_path = os.path.join(temp_dir, "PySocks-master.tar.gz")
        
        if not download_file(url, tarfile_path):
            return False
        
        # 解压
        if not extract_tarfile(tarfile_path, temp_dir):
            return False
        
        # 查找解压后的目录
        source_dir = os.path.join(temp_dir, "PySocks-master")
        if not os.path.exists(source_dir):
            log_error("解压后的源码目录不存在")
            return False
        
        # 检查setup.py
        setup_py = os.path.join(source_dir, "setup.py")
        if not os.path.exists(setup_py):
            log_error("setup.py文件不存在")
            return False
        
        # 安装
        log_info("执行安装...")
        cmd = [sys.executable, "setup.py", "install", "--user"]
        if not run_command(cmd, cwd=source_dir):
            return False
        
        log_success("PySocks安装完成")
        return True

def verify_installation():
    """验证安装"""
    log_info("验证PySocks安装...")
    
    try:
        import socks
        log_success("✅ PySocks验证成功")
        
        # 显示安装信息
        print(f"版本: {socks.__version__}")
        print(f"模块路径: {socks.__file__}")
        print(f"支持协议: SOCKS4={socks.SOCKS4}, SOCKS5={socks.SOCKS5}, HTTP={socks.HTTP}")
        
        return True
    except ImportError as e:
        log_error(f"❌ PySocks验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 PySocks一键安装脚本 (Python版本)")
    print("=" * 40)
    print(f"开始时间: {subprocess.run(['date'], capture_output=True, text=True).stdout.strip()}")
    print()
    
    # 检查Python版本
    log_info(f"Python版本: {sys.version}")
    
    # 处理命令行参数
    force_install = "--force" in sys.argv or "-f" in sys.argv
    show_help = "--help" in sys.argv or "-h" in sys.argv
    
    if show_help:
        print("PySocks一键安装脚本")
        print()
        print("用法:")
        print(f"  {sys.argv[0]}           # 正常安装（如已安装则跳过）")
        print(f"  {sys.argv[0]} --force   # 强制重新安装")
        print(f"  {sys.argv[0]} --help    # 显示帮助信息")
        print()
        print("功能:")
        print("  - 自动从GitHub下载PySocks源码")
        print("  - 编译并安装到用户目录")
        print("  - 验证安装是否成功")
        print("  - 自动清理临时文件")
        return 0
    
    # 检查是否已安装
    if not force_install and check_pysocks():
        print()
        log_info(f"如需重新安装，请使用参数: {sys.argv[0]} --force")
        return 0
    
    if force_install:
        log_warning("强制重新安装模式")
    
    # 安装PySocks
    try:
        if install_pysocks():
            log_success("PySocks安装完成")
        else:
            log_error("PySocks安装失败")
            return 1
        
        # 验证安装
        if verify_installation():
            log_success("所有步骤完成")
        else:
            log_error("验证失败")
            return 1
        
        print()
        print("=" * 40)
        log_success("🎉 PySocks安装成功！")
        print()
        log_info("现在可以使用pip安装其他依赖:")
        print("  pip install -r requirements.txt")
        print()
        print(f"结束时间: {subprocess.run(['date'], capture_output=True, text=True).stdout.strip()}")
        
        return 0
        
    except KeyboardInterrupt:
        log_warning("用户中断安装")
        return 1
    except Exception as e:
        log_error(f"安装过程中出现异常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
