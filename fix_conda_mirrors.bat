@echo off
REM conda镜像源修复脚本 - Windows版本

echo 🔧 修复conda镜像源配置...

REM 1. 清理现有配置
echo 1. 清理现有镜像源配置...
conda config --remove-key channels 2>nul

REM 2. 重置为默认配置
echo 2. 重置conda配置...
conda config --set show_channel_urls yes
conda config --set channel_priority strict

REM 3. 添加稳定的国内镜像源
echo 3. 配置稳定的镜像源...
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch

REM 4. 设置网络超时参数
echo 4. 优化网络参数...
conda config --set remote_connect_timeout_secs 30
conda config --set remote_read_timeout_secs 60
conda config --set remote_max_retries 5

echo ✅ conda镜像源配置完成！
echo.
echo 📋 当前配置：
conda config --show channels
echo.
echo 🧪 测试连接：
conda search python --info | findstr /C:"python"

pause
