# Conda环境配置文件 - CPU版本
# 专门针对CPU环境的配置（无GPU支持）

name: multimodal-cpu

channels:
  - pytorch
  - conda-forge
  - defaults

dependencies:
  # Python版本
  - python=3.9

  # 深度学习框架 (CPU版本)
  - pytorch=2.1.0
  - torchvision=0.16.0
  - torchaudio=2.1.0
  - cpuonly

  # 数据处理核心库
  - numpy=1.24.3
  - pandas=2.0.3

  # 图像处理
  - opencv=4.8.0
  - pillow=10.0.0

  # 可视化
  - matplotlib=3.7.2
  - seaborn=0.12.2

  # 机器学习工具
  - scikit-learn=1.3.0

  # 系统工具
  - tqdm=4.65.0
  - pyyaml=6.0.1
  - tensorboard=2.14.0

  # pip包管理
  - pip=23.2.1
  - pip:
    - ultralytics==8.0.196
    - albumentations==1.3.1
    - pathlib2==2.3.7
