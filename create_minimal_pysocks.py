#!/usr/bin/env python3
"""
创建最小化的PySocks模块
仅用于满足pip的SOCKS依赖要求
"""

import os
import sys
import site

def create_minimal_pysocks():
    """创建最小化的PySocks模块"""
    
    # 获取用户site-packages目录
    user_site = site.getusersitepackages()
    if not os.path.exists(user_site):
        os.makedirs(user_site)
    
    # 创建socks模块目录
    socks_dir = os.path.join(user_site, 'socks')
    if not os.path.exists(socks_dir):
        os.makedirs(socks_dir)
    
    # 创建__init__.py
    init_content = '''"""
Minimal PySocks implementation
仅用于满足pip的SOCKS依赖要求
"""

__version__ = "1.7.1"
__author__ = "Anorov"

# SOCKS协议常量
SOCKS4 = 1
SOCKS5 = 2
HTTP = 3

# 错误类
class ProxyError(Exception):
    pass

class GeneralProxyError(ProxyError):
    pass

class ProxyConnectionError(ProxyError):
    pass

class SOCKS4Error(ProxyError):
    pass

class SOCKS5Error(ProxyError):
    pass

class HTTPError(ProxyError):
    pass

# 基本函数（空实现）
def setdefaultproxy(*args, **kwargs):
    """设置默认代理（空实现）"""
    pass

def wrapmodule(*args, **kwargs):
    """包装模块（空实现）"""
    pass

def create_connection(*args, **kwargs):
    """创建连接（空实现）"""
    import socket
    return socket.create_connection(*args, **kwargs)

# 兼容性
socksocket = None
'''

    # 写入__init__.py
    init_path = os.path.join(socks_dir, '__init__.py')
    with open(init_path, 'w', encoding='utf-8') as f:
        f.write(init_content)
    
    # 创建PySocks-1.7.1.dist-info目录（让pip认为已安装）
    dist_info_dir = os.path.join(user_site, 'PySocks-1.7.1.dist-info')
    if not os.path.exists(dist_info_dir):
        os.makedirs(dist_info_dir)
    
    # 创建METADATA文件
    metadata_content = '''Name: PySocks
Version: 1.7.1
Summary: A Python SOCKS client module (minimal implementation)
Author: Anorov
License: BSD
'''
    
    metadata_path = os.path.join(dist_info_dir, 'METADATA')
    with open(metadata_path, 'w', encoding='utf-8') as f:
        f.write(metadata_content)
    
    # 创建INSTALLER文件
    installer_path = os.path.join(dist_info_dir, 'INSTALLER')
    with open(installer_path, 'w', encoding='utf-8') as f:
        f.write('manual\n')
    
    # 创建RECORD文件
    record_content = f'''socks/__init__.py,,
PySocks-1.7.1.dist-info/METADATA,,
PySocks-1.7.1.dist-info/INSTALLER,,
PySocks-1.7.1.dist-info/RECORD,,
'''
    
    record_path = os.path.join(dist_info_dir, 'RECORD')
    with open(record_path, 'w', encoding='utf-8') as f:
        f.write(record_content)
    
    print(f"✅ 最小化PySocks模块已创建在: {user_site}")
    print(f"📁 模块目录: {socks_dir}")
    print(f"📁 包信息目录: {dist_info_dir}")
    
    # 验证安装
    try:
        import socks
        print(f"✅ PySocks导入成功，版本: {socks.__version__}")
        return True
    except ImportError as e:
        print(f"❌ PySocks导入失败: {e}")
        return False

if __name__ == '__main__':
    print("🔧 创建最小化PySocks模块...")
    success = create_minimal_pysocks()
    
    if success:
        print("\n🎉 PySocks模块创建成功！")
        print("现在可以尝试安装其他依赖:")
        print("pip install -r /home/<USER>/yyb/MultiModal/requirements/requirements.txt")
    else:
        print("\n❌ PySocks模块创建失败")
        sys.exit(1)
