#!/bin/bash
# 完全绕过代理问题的安装脚本

echo "🔧 绕过代理问题安装Python依赖..."

# 1. 彻底清除代理
echo "1️⃣ 清除所有代理设置..."
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY socks_proxy SOCKS_PROXY all_proxy ALL_PROXY ftp_proxy FTP_PROXY

# 2. 备份并修改pip配置
echo "2️⃣ 配置pip..."
mkdir -p ~/.pip
cp ~/.pip/pip.conf ~/.pip/pip.conf.backup 2>/dev/null || true

cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.org/simple/
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
timeout = 60
retries = 5
no-cache-dir = true
disable-pip-version-check = true

[install]
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
EOF

# 3. 尝试多种方法安装PySocks
echo "3️⃣ 安装PySocks..."

# 方法A: 直接pip安装
echo "  尝试方法A: 直接pip安装..."
if pip install PySocks --no-warn-script-location 2>/dev/null; then
    echo "  ✅ 方法A成功"
    PYSOCKS_INSTALLED=true
else
    echo "  ❌ 方法A失败"
    PYSOCKS_INSTALLED=false
fi

# 方法B: 下载源码安装
if [ "$PYSOCKS_INSTALLED" = false ]; then
    echo "  尝试方法B: 源码安装..."
    TEMP_DIR="/tmp/pysocks_source_$$"
    mkdir -p "$TEMP_DIR"
    cd "$TEMP_DIR"
    
    if curl -L -o PySocks-1.7.1.tar.gz "https://github.com/Anorov/PySocks/archive/refs/tags/1.7.1.tar.gz" 2>/dev/null; then
        tar -xzf PySocks-1.7.1.tar.gz 2>/dev/null
        cd PySocks-1.7.1
        if python3 setup.py install --user 2>/dev/null; then
            echo "  ✅ 方法B成功"
            PYSOCKS_INSTALLED=true
        else
            echo "  ❌ 方法B失败"
        fi
    else
        echo "  ❌ 方法B下载失败"
    fi
    
    cd ~
    rm -rf "$TEMP_DIR"
fi

# 方法C: 系统包管理器
if [ "$PYSOCKS_INSTALLED" = false ]; then
    echo "  尝试方法C: 系统包管理器..."
    if command -v apt >/dev/null 2>&1; then
        if sudo apt update && sudo apt install -y python3-socks 2>/dev/null; then
            echo "  ✅ 方法C成功"
            PYSOCKS_INSTALLED=true
        fi
    fi
fi

# 4. 验证PySocks安装
echo "4️⃣ 验证PySocks安装..."
if python3 -c "import socks; print('PySocks version:', socks.__version__)" 2>/dev/null; then
    echo "✅ PySocks安装成功"
else
    echo "❌ PySocks安装失败，尝试继续..."
fi

# 5. 安装项目依赖
echo "5️⃣ 安装项目依赖..."
if pip install -r /home/<USER>/yyb/MultiModal/requirements/requirements.txt --no-warn-script-location; then
    echo "✅ 项目依赖安装成功！"
else
    echo "❌ 项目依赖安装失败，尝试分批安装..."
    
    # 分批安装关键包
    CRITICAL_PACKAGES=(
        "torch>=2.0.0"
        "torchvision>=0.15.0"
        "numpy>=1.24.0"
        "opencv-python>=4.8.0"
        "matplotlib>=3.7.0"
        "pandas>=2.0.0"
        "PyYAML>=6.0"
        "tqdm>=4.65.0"
        "ultralytics>=8.0.0"
    )
    
    for package in "${CRITICAL_PACKAGES[@]}"; do
        echo "  安装: $package"
        pip install "$package" --no-warn-script-location 2>/dev/null || echo "    ⚠️ $package 安装失败"
    done
fi

# 6. 恢复pip配置
echo "6️⃣ 恢复配置..."
if [ -f ~/.pip/pip.conf.backup ]; then
    mv ~/.pip/pip.conf.backup ~/.pip/pip.conf
else
    rm -f ~/.pip/pip.conf
fi

# 7. 验证安装
echo "7️⃣ 验证关键包..."
python3 -c "
packages = ['torch', 'cv2', 'numpy', 'matplotlib', 'pandas', 'yaml']
for pkg in packages:
    try:
        __import__(pkg)
        print(f'✅ {pkg}: OK')
    except ImportError:
        print(f'❌ {pkg}: 未安装')
"

echo "🎉 安装脚本完成！"
