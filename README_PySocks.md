# PySocks一键安装工具

## 📋 问题描述

在SSH环境下使用pip安装Python包时，经常遇到以下错误：
```
ERROR: Could not install packages due to an OSError: Missing dependencies for SOCKS support.
```

这是因为系统配置了SOCKS代理，但pip缺少PySocks依赖包，而安装PySocks本身也需要通过代理，形成了循环依赖问题。

## 🚀 解决方案

本工具提供了两个版本的一键安装脚本，可以绕过代理问题直接从GitHub源码安装PySocks。

## 📁 文件说明

- **`install_pysocks.sh`** - Bash版本的安装脚本
- **`install_pysocks.py`** - Python版本的安装脚本  
- **`README_PySocks.md`** - 本说明文档

## 🔧 使用方法

### 方法1：使用Bash脚本（推荐）

```bash
# 给脚本执行权限
chmod +x install_pysocks.sh

# 正常安装
./install_pysocks.sh

# 强制重新安装
./install_pysocks.sh --force

# 查看帮助
./install_pysocks.sh --help
```

### 方法2：使用Python脚本

```bash
# 正常安装
python3 install_pysocks.py

# 强制重新安装
python3 install_pysocks.py --force

# 查看帮助
python3 install_pysocks.py --help
```

### 方法3：一行命令（快速版本）

```bash
curl -L https://github.com/Anorov/PySocks/archive/refs/heads/master.tar.gz | tar -xz && cd PySocks-master && python3 setup.py install --user && cd .. && rm -rf PySocks-master
```

## ✅ 验证安装

安装完成后，可以通过以下命令验证：

```bash
python3 -c "import socks; print('PySocks version:', socks.__version__)"
```

如果输出类似以下内容，说明安装成功：
```
PySocks version: 1.7.1
```

## 🎯 后续步骤

PySocks安装成功后，就可以正常使用pip安装其他依赖了：

```bash
# 安装项目依赖
pip install -r requirements.txt

# 或安装单个包
pip install torch torchvision opencv-python
```

## 🔍 脚本功能特点

### 自动化功能
- ✅ 自动检测PySocks是否已安装
- ✅ 自动从GitHub下载最新源码
- ✅ 自动编译并安装到用户目录
- ✅ 自动验证安装是否成功
- ✅ 自动清理临时文件

### 安全特性
- ✅ 使用临时目录，不污染当前目录
- ✅ 安装到用户目录，不需要sudo权限
- ✅ 错误处理和回滚机制
- ✅ 详细的日志输出

### 兼容性
- ✅ 支持Ubuntu/Debian/CentOS等Linux发行版
- ✅ 支持Python 3.6+
- ✅ 兼容各种网络环境和代理配置

## 🐛 故障排除

### 问题1：网络连接失败
```bash
# 解决方案：临时禁用代理
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY
./install_pysocks.sh
```

### 问题2：权限问题
```bash
# 解决方案：确保有用户目录写权限
ls -la ~/.local/lib/python*/site-packages/
```

### 问题3：Python版本不兼容
```bash
# 解决方案：检查Python版本
python3 --version
# 确保使用Python 3.6+
```

## 📞 技术支持

如果遇到问题，可以：

1. **查看详细日志**：脚本会输出详细的执行日志
2. **手动验证**：按照脚本步骤手动执行
3. **检查环境**：确认Python版本和网络连接
4. **使用备选方案**：尝试系统包管理器安装

## 🔄 更新日志

- **v1.0** - 初始版本，支持基本的PySocks安装
- **v1.1** - 添加Python版本脚本
- **v1.2** - 增强错误处理和日志输出
- **v1.3** - 添加强制重装和帮助功能

## 📄 许可证

本工具遵循MIT许可证，可自由使用和修改。

---

**💡 提示**：安装完PySocks后，建议重启终端会话以确保环境变量生效。
