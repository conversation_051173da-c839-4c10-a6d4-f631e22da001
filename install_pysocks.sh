#!/bin/bash
# PySocks一键安装脚本
# 解决SSH环境下SOCKS代理依赖问题
# 使用方法: bash install_pysocks.sh 或 ./install_pysocks.sh

set -e  # 遇到错误立即退出

echo "🚀 PySocks一键安装脚本"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否已安装PySocks
check_pysocks() {
    log_info "检查PySocks是否已安装..."
    if python3 -c "import socks; print('PySocks version:', socks.__version__)" 2>/dev/null; then
        log_success "PySocks已安装，无需重复安装"
        return 0
    else
        log_info "PySocks未安装，开始安装..."
        return 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -f PySocks-*.tar.gz PySocks-*.whl
    rm -rf PySocks-master PySocks-*
    log_success "清理完成"
}

# 备份当前目录
ORIGINAL_DIR=$(pwd)

# 创建临时工作目录
TEMP_DIR="/tmp/pysocks_install_$$"
mkdir -p "$TEMP_DIR"
cd "$TEMP_DIR"

# 安装PySocks
install_pysocks() {
    log_info "开始安装PySocks..."
    
    # 清理之前的文件
    log_info "清理旧文件..."
    cleanup
    
    # 下载GitHub源码
    log_info "从GitHub下载PySocks源码..."
    if curl -L -o PySocks-master.tar.gz "https://github.com/Anorov/PySocks/archive/refs/heads/master.tar.gz"; then
        log_success "源码下载成功"
    else
        log_error "源码下载失败"
        return 1
    fi
    
    # 检查下载文件
    if [ ! -f "PySocks-master.tar.gz" ] || [ ! -s "PySocks-master.tar.gz" ]; then
        log_error "下载的文件不存在或为空"
        return 1
    fi
    
    log_info "文件大小: $(ls -lh PySocks-master.tar.gz | awk '{print $5}')"
    
    # 解压
    log_info "解压源码包..."
    if tar -xzf PySocks-master.tar.gz; then
        log_success "解压成功"
    else
        log_error "解压失败"
        return 1
    fi
    
    # 检查解压目录
    if [ ! -d "PySocks-master" ]; then
        log_error "解压目录不存在"
        return 1
    fi
    
    # 进入目录并安装
    log_info "进入源码目录并安装..."
    cd PySocks-master
    
    # 检查setup.py是否存在
    if [ ! -f "setup.py" ]; then
        log_error "setup.py文件不存在"
        return 1
    fi
    
    # 安装到用户目录
    log_info "执行安装命令..."
    if python3 setup.py install --user; then
        log_success "PySocks安装成功"
    else
        log_error "PySocks安装失败"
        return 1
    fi
    
    # 返回临时目录
    cd ..
    
    return 0
}

# 验证安装
verify_installation() {
    log_info "验证PySocks安装..."
    
    if python3 -c "import socks; print('PySocks installed successfully, version:', socks.__version__)" 2>/dev/null; then
        log_success "✅ PySocks验证成功"
        
        # 显示安装信息
        python3 -c "
import socks
import os
print(f'版本: {socks.__version__}')
print(f'模块路径: {socks.__file__}')
print(f'支持协议: SOCKS4={socks.SOCKS4}, SOCKS5={socks.SOCKS5}, HTTP={socks.HTTP}')
"
        return 0
    else
        log_error "❌ PySocks验证失败"
        return 1
    fi
}

# 主函数
main() {
    echo "开始时间: $(date)"
    echo ""
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装，请先安装Python3"
        exit 1
    fi
    
    log_info "Python版本: $(python3 --version)"
    
    # 检查是否已安装
    if check_pysocks; then
        echo ""
        log_info "如需重新安装，请使用参数: $0 --force"
        exit 0
    fi
    
    # 安装PySocks
    if install_pysocks; then
        log_success "PySocks安装完成"
    else
        log_error "PySocks安装失败"
        exit 1
    fi
    
    # 验证安装
    if verify_installation; then
        log_success "所有步骤完成"
    else
        log_error "验证失败"
        exit 1
    fi
    
    echo ""
    echo "================================"
    log_success "🎉 PySocks安装成功！"
    echo ""
    log_info "现在可以使用pip安装其他依赖:"
    echo "  pip install -r requirements.txt"
    echo ""
    echo "结束时间: $(date)"
}

# 清理函数（脚本退出时调用）
cleanup_on_exit() {
    cd "$ORIGINAL_DIR"
    rm -rf "$TEMP_DIR"
}

# 设置退出时清理
trap cleanup_on_exit EXIT

# 处理命令行参数
if [ "$1" = "--force" ] || [ "$1" = "-f" ]; then
    log_warning "强制重新安装模式"
    # 跳过检查，直接安装
    if install_pysocks && verify_installation; then
        log_success "🎉 强制重新安装完成！"
    else
        log_error "强制重新安装失败"
        exit 1
    fi
elif [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "PySocks一键安装脚本"
    echo ""
    echo "用法:"
    echo "  $0           # 正常安装（如已安装则跳过）"
    echo "  $0 --force   # 强制重新安装"
    echo "  $0 --help    # 显示帮助信息"
    echo ""
    echo "功能:"
    echo "  - 自动从GitHub下载PySocks源码"
    echo "  - 编译并安装到用户目录"
    echo "  - 验证安装是否成功"
    echo "  - 自动清理临时文件"
    exit 0
else
    # 正常安装流程
    main
fi
