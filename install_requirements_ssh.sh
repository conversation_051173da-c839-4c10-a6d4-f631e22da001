#!/bin/bash
# SSH环境下安装Python依赖的脚本
# 解决SOCKS代理和网络连接问题

echo "🔧 SSH环境下安装Python依赖..."

# 保存当前代理设置
ORIGINAL_HTTP_PROXY=$http_proxy
ORIGINAL_HTTPS_PROXY=$https_proxy
ORIGINAL_ALL_PROXY=$all_proxy

echo "📋 当前代理设置:"
echo "  HTTP_PROXY: $HTTP_PROXY"
echo "  HTTPS_PROXY: $HTTPS_PROXY"
echo "  ALL_PROXY: $ALL_PROXY"

# 方案1: 先尝试安装SOCKS支持
echo ""
echo "🔄 方案1: 安装SOCKS支持..."
if pip install PySocks --no-warn-script-location 2>/dev/null; then
    echo "✅ SOCKS支持安装成功，尝试正常安装..."
    if pip install -r /home/<USER>/yyb/MultiModal/requirements/requirements.txt; then
        echo "✅ 依赖安装成功！"
        exit 0
    fi
fi

# 方案2: 临时禁用代理
echo ""
echo "🔄 方案2: 临时禁用代理..."
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY socks_proxy SOCKS_PROXY all_proxy ALL_PROXY

if pip install -r /home/<USER>/yyb/MultiModal/requirements/requirements.txt; then
    echo "✅ 依赖安装成功！"
    # 恢复代理设置
    export http_proxy=$ORIGINAL_HTTP_PROXY
    export https_proxy=$ORIGINAL_HTTPS_PROXY
    export all_proxy=$ORIGINAL_ALL_PROXY
    exit 0
fi

# 方案3: 使用不同镜像源
echo ""
echo "🔄 方案3: 尝试不同的PyPI镜像源..."

MIRRORS=(
    "https://pypi.tuna.tsinghua.edu.cn/simple/"
    "https://mirrors.aliyun.com/pypi/simple/"
    "https://pypi.douban.com/simple/"
    "https://mirrors.cloud.tencent.com/pypi/simple/"
)

for mirror in "${MIRRORS[@]}"; do
    echo "  尝试镜像: $mirror"
    if pip install -r /home/<USER>/yyb/MultiModal/requirements/requirements.txt -i "$mirror" --trusted-host $(echo $mirror | cut -d'/' -f3); then
        echo "✅ 使用镜像 $mirror 安装成功！"
        # 恢复代理设置
        export http_proxy=$ORIGINAL_HTTP_PROXY
        export https_proxy=$ORIGINAL_HTTPS_PROXY
        export all_proxy=$ORIGINAL_ALL_PROXY
        exit 0
    fi
done

# 方案4: 分批安装关键包
echo ""
echo "🔄 方案4: 分批安装关键包..."

CRITICAL_PACKAGES=(
    "torch>=2.0.0"
    "torchvision>=0.15.0"
    "numpy>=1.24.0"
    "opencv-python>=4.8.0"
    "ultralytics>=8.0.0"
    "matplotlib>=3.7.0"
    "pandas>=2.0.0"
    "PyYAML>=6.0"
    "tqdm>=4.65.0"
)

for package in "${CRITICAL_PACKAGES[@]}"; do
    echo "  安装: $package"
    pip install "$package" -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn
done

echo ""
echo "🔄 安装剩余包..."
pip install -r /home/<USER>/yyb/MultiModal/requirements/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn

# 恢复代理设置
export http_proxy=$ORIGINAL_HTTP_PROXY
export https_proxy=$ORIGINAL_HTTPS_PROXY
export all_proxy=$ORIGINAL_ALL_PROXY

echo ""
echo "📋 验证安装..."
python3 -c "
try:
    import torch
    import cv2
    import numpy as np
    print('✅ 关键包导入成功')
    print(f'PyTorch版本: {torch.__version__}')
    print(f'OpenCV版本: {cv2.__version__}')
    print(f'NumPy版本: {np.__version__}')
except ImportError as e:
    print(f'❌ 导入失败: {e}')
"

echo "🎉 安装脚本执行完成！"
